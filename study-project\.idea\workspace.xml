<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="ebc15272-3b84-403f-aea8-95855ddadaa8" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/Ai-im/pom.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/StudyImApplication.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/config/CorsConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/config/DataInitializer.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/config/GlobalExceptionHandler.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/config/NettyConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/config/RedisConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/config/ScheduleConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/config/SystemConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/controller/AuthController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/controller/FriendController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/controller/HealthController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/controller/MessageController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/controller/StatsController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/dto/ApiResponse.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/dto/FriendInfo.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/dto/LoginRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/dto/MessageDto.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/dto/RegisterRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/dto/SendMessageRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/entity/FriendRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/entity/Friendship.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/entity/Message.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/entity/User.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/netty/CorsHandler.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/netty/NettyServer.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/netty/WebSocketHandler.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/repository/FriendRequestRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/repository/FriendshipRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/repository/MessageRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/repository/UserRepository.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/service/FriendService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/service/InMemoryUserStatusService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/service/MessageService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/service/RedisService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/service/UserService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/java/com/zhentao/studyim/util/JwtUtil.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/resources/application-prod.yml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/resources/application.yml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/main/resources/schema.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Ai-im/src/test/java/com/zhentao/studyim/StudyImApplicationTests.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/encodings.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/encodings.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Ai-userlogin/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/Ai-userlogin/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ArcFace64.dat" beforeDir="false" afterPath="$PROJECT_DIR$/ArcFace64.dat" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/s-sai/unpackage/dist/cache/.vite/deps/_metadata.json" beforeDir="false" afterPath="$PROJECT_DIR$/s-sai/unpackage/dist/cache/.vite/deps/_metadata.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/s-sai/unpackage/dist/cache/.vite/deps/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/s-sai/unpackage/dist/cache/.vite/deps/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/s-sai/unpackage/dist/cache/.vite/deps_temp_18ffd52b/package.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/.sourcemap/mp-weixin/common/vendor.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/.sourcemap/mp-weixin/common/vendor.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/.sourcemap/mp-weixin/pages/ai-emotion/ai-emotion.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/.sourcemap/mp-weixin/pages/ai-emotion/ai-emotion.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/.sourcemap/mp-weixin/pages/ai-home/ai-home.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/.sourcemap/mp-weixin/pages/ai-home/ai-home.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/.sourcemap/mp-weixin/pages/ai-knowledge/ai-knowledge.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/.sourcemap/mp-weixin/pages/ai-knowledge/ai-knowledge.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/.sourcemap/mp-weixin/pages/ai-recommend/ai-recommend.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/.sourcemap/mp-weixin/pages/ai-recommend/ai-recommend.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/.sourcemap/mp-weixin/pages/ai-search/ai-search.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/.sourcemap/mp-weixin/pages/ai-search/ai-search.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/.sourcemap/mp-weixin/pages/ai-translate/ai-translate.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/.sourcemap/mp-weixin/pages/ai-translate/ai-translate.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/.sourcemap/mp-weixin/pages/ai-writing/ai-writing.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/.sourcemap/mp-weixin/pages/ai-writing/ai-writing.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/.sourcemap/mp-weixin/pages/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/.sourcemap/mp-weixin/pages/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/.sourcemap/mp-weixin/pages/index/index.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/.sourcemap/mp-weixin/pages/index/index.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/.sourcemap/mp-weixin/pages/login/login.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/.sourcemap/mp-weixin/pages/login/login.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/.sourcemap/mp-weixin/pages/register/register.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/.sourcemap/mp-weixin/pages/register/register.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/mp-weixin/ai-home.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/mp-weixin/common/vendor.js" beforeDir="false" afterPath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/mp-weixin/common/vendor.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/mp-weixin/components/bottom-nav/bottom-nav.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/mp-weixin/components/bottom-nav/bottom-nav.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/mp-weixin/components/bottom-nav/bottom-nav.wxml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/mp-weixin/components/bottom-nav/bottom-nav.wxss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/mp-weixin/pages/ai-home/ai-home2.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/mp-weixin/pages/main/main.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/mp-weixin/pages/main/main.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/mp-weixin/pages/main/main.wxml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/mp-weixin/pages/main/main.wxss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/mp-weixin/project.config.json" beforeDir="false" afterPath="$PROJECT_DIR$/s-sai/unpackage/dist/dev/mp-weixin/project.config.json" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="userSettingsFile" value="D:\apache-maven-3.9.9\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="30ZkANiQTp332gJGwSafbdOjQm7" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven. [org.apache.maven.plugins:maven-archetype-plugin:RELEASE:generate].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.AppServiceApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.ArcsoftFaceServiceApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.GatewayApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.PcServiceApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.StudyImApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.UserLoginApplication.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/study/studyuProject/study-project&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;run.configurations.included.in.services&quot;: &quot;true&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\study\studyProject\study-project\Ai-im" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
    <option name="hiddenConfigurations">
      <map>
        <entry key="SpringBootApplicationConfigurationType">
          <value>
            <set>
              <option value="PcServiceApplication" />
            </set>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.StudyImApplication">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="AiApp-service" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="AiApp-service" />
      <option name="filePath" />
      <method v="2" />
    </configuration>
    <configuration name="AppServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="AiApp-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.zhentao.AppServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ArcsoftFaceServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="Ai-face" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.zhentao.ArcsoftFaceServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GatewayApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="Ai-gateway" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.zhentao.GatewayApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="PcServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="AiPC-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.zhentao.PcServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="StudyImApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="Ai-im" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zhentao.studyim.StudyImApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UserLoginApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="Ai-userlogin" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.zhentao.UserLoginApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.27812.49" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.27812.49" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="ebc15272-3b84-403f-aea8-95855ddadaa8" name="Changes" comment="" />
      <created>1753842339331</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753842339331</updated>
      <workItem from="1753842340721" duration="7627000" />
      <workItem from="1753876384337" duration="2339000" />
      <workItem from="1753878804594" duration="1358000" />
      <workItem from="1753922521986" duration="269000" />
      <workItem from="1753922920797" duration="1977000" />
      <workItem from="1753945579669" duration="974000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>